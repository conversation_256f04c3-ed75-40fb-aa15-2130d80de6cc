#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR配置测试脚本 - 诊断OCR识别问题
"""

import os
import sys
import time
import logging
from pathlib import Path

# 设置控制台编码
if sys.platform == 'win32':
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_paddle_installation():
    """测试PaddleOCR安装"""
    print("=== 测试PaddleOCR安装 ===")
    try:
        import paddle
        print(f"[OK] PaddlePaddle版本: {paddle.__version__}")

        # 检查CUDA支持
        if paddle.device.is_compiled_with_cuda():
            print(f"[OK] CUDA支持: 已编译")
            gpu_count = paddle.device.cuda.device_count()
            print(f"[OK] GPU数量: {gpu_count}")
            if gpu_count > 0:
                for i in range(gpu_count):
                    gpu_name = paddle.device.cuda.get_device_name(i)
                    print(f"   GPU {i}: {gpu_name}")
        else:
            print("[WARN] CUDA支持: 未编译")

    except ImportError as e:
        print(f"[ERROR] PaddlePaddle导入失败: {e}")
        return False
    except Exception as e:
        print(f"[ERROR] PaddlePaddle检查失败: {e}")
        return False

    try:
        from paddleocr import PaddleOCR
        print(f"[OK] PaddleOCR导入成功")
        return True
    except ImportError as e:
        print(f"[ERROR] PaddleOCR导入失败: {e}")
        return False

def test_ocr_models():
    """测试不同OCR模型"""
    print("\n=== 测试OCR模型初始化 ===")
    
    try:
        from paddleocr import PaddleOCR
        
        models = [
            ('PP-OCRv2', 'PP-OCRv2'),
            ('PP-OCRv3', 'PP-OCRv3'), 
            ('PP-OCRv4', 'PP-OCRv4')
        ]
        
        for model_name, model_version in models:
            print(f"\n--- 测试 {model_name} ---")
            try:
                start_time = time.time()
                ocr = PaddleOCR(
                    use_angle_cls=True,
                    lang='ch',
                    use_gpu=False,  # 先测试CPU版本
                    show_log=False,
                    ocr_version=model_version
                )
                init_time = time.time() - start_time
                print(f"✅ {model_name} 初始化成功 ({init_time:.2f}s)")
                
                # 测试一个简单的识别
                test_result = ocr.ocr("test_image.png", cls=True) if os.path.exists("test_image.png") else None
                if test_result is not None:
                    print(f"✅ {model_name} 识别测试成功")
                else:
                    print(f"⚠️ {model_name} 无测试图片")
                    
            except Exception as e:
                print(f"❌ {model_name} 初始化失败: {e}")
                
    except ImportError:
        print("❌ 无法导入PaddleOCR")

def test_image_processing():
    """测试图片处理功能"""
    print("\n=== 测试图片处理 ===")
    
    try:
        from PIL import Image, ImageEnhance, ImageFilter
        import io
        
        # 创建一个测试图片
        test_img = Image.new('RGB', (200, 100), color='white')
        
        # 测试图片增强
        enhancer = ImageEnhance.Contrast(test_img)
        enhanced = enhancer.enhance(1.2)
        print("✅ 对比度增强测试成功")
        
        enhancer = ImageEnhance.Sharpness(enhanced)
        enhanced = enhancer.enhance(1.1)
        print("✅ 锐度增强测试成功")
        
        # 测试滤镜
        filtered = enhanced.filter(ImageFilter.MedianFilter(size=3))
        print("✅ 中值滤波测试成功")
        
        # 测试格式转换
        output = io.BytesIO()
        filtered.save(output, format='PNG', optimize=True)
        print("✅ 图片格式转换测试成功")
        
    except Exception as e:
        print(f"❌ 图片处理测试失败: {e}")

def check_ocr_config():
    """检查OCR配置"""
    print("\n=== 检查OCR配置 ===")
    
    # 检查环境变量
    glog_level = os.environ.get('GLOG_minloglevel', '未设置')
    print(f"GLOG_minloglevel: {glog_level}")
    
    # 检查模型缓存目录
    home_dir = Path.home()
    paddle_cache = home_dir / '.paddleocr'
    if paddle_cache.exists():
        print(f"✅ PaddleOCR缓存目录存在: {paddle_cache}")
        
        # 列出已下载的模型
        for model_dir in paddle_cache.iterdir():
            if model_dir.is_dir():
                print(f"   模型目录: {model_dir.name}")
    else:
        print(f"⚠️ PaddleOCR缓存目录不存在: {paddle_cache}")

def test_language_support():
    """测试语言支持"""
    print("\n=== 测试语言支持 ===")
    
    try:
        from paddleocr import PaddleOCR
        
        languages = ['ch', 'en', 'chinese_cht']
        
        for lang in languages:
            try:
                print(f"测试语言: {lang}")
                ocr = PaddleOCR(
                    use_angle_cls=True,
                    lang=lang,
                    use_gpu=False,
                    show_log=False
                )
                print(f"✅ {lang} 语言支持正常")
            except Exception as e:
                print(f"❌ {lang} 语言支持失败: {e}")
                
    except ImportError:
        print("❌ 无法导入PaddleOCR")

def main():
    """主测试函数"""
    print("OCR配置诊断工具")
    print("=" * 50)
    
    # 测试安装
    if not test_paddle_installation():
        print("\n❌ PaddleOCR安装有问题，请检查安装")
        return
    
    # 测试配置
    check_ocr_config()
    
    # 测试图片处理
    test_image_processing()
    
    # 测试模型
    test_ocr_models()
    
    # 测试语言支持
    test_language_support()
    
    print("\n=== 诊断完成 ===")
    print("如果所有测试都通过，OCR配置应该是正常的")
    print("如果识别效果不好，可能是图片质量或内容的问题")

if __name__ == "__main__":
    main()
