# -*- coding: utf-8 -*-
"""
简单OCR测试脚本
"""

import os
import sys

def test_basic_ocr():
    """测试基本OCR功能"""
    print("=== OCR基本测试 ===")
    
    try:
        # 导入PaddleOCR
        from paddleocr import PaddleOCR
        print("[OK] PaddleOCR导入成功")
        
        # 初始化OCR
        print("正在初始化OCR...")
        ocr = PaddleOCR(
            use_angle_cls=True,
            lang='ch',
            use_gpu=False,
            show_log=False,
            ocr_version='PP-OCRv4'
        )
        print("[OK] OCR初始化成功")
        
        # 检查当前目录是否有测试图片
        test_images = []
        for ext in ['*.png', '*.jpg', '*.jpeg']:
            import glob
            test_images.extend(glob.glob(ext))
        
        if test_images:
            print(f"找到测试图片: {test_images[0]}")
            
            # 进行OCR识别
            result = ocr.ocr(test_images[0], cls=True)
            
            if result and result[0]:
                print("[OK] OCR识别成功")
                print("识别结果:")
                for line in result[0]:
                    text = line[1][0]
                    confidence = line[1][1]
                    print(f"  文本: {text} (置信度: {confidence:.2f})")
            else:
                print("[WARN] 未识别到文本")
        else:
            print("[INFO] 当前目录没有测试图片")
            
        return True
        
    except ImportError as e:
        print(f"[ERROR] 导入失败: {e}")
        return False
    except Exception as e:
        print(f"[ERROR] OCR测试失败: {e}")
        return False

def check_model_config():
    """检查模型配置"""
    print("\n=== 检查模型配置 ===")
    
    try:
        from paddleocr import PaddleOCR
        
        # 测试不同模型版本
        versions = ['PP-OCRv2', 'PP-OCRv3', 'PP-OCRv4']
        
        for version in versions:
            try:
                print(f"测试 {version}...")
                ocr = PaddleOCR(
                    use_angle_cls=True,
                    lang='ch',
                    use_gpu=False,
                    show_log=False,
                    ocr_version=version
                )
                print(f"[OK] {version} 初始化成功")
            except Exception as e:
                print(f"[ERROR] {version} 初始化失败: {e}")
                
    except ImportError:
        print("[ERROR] 无法导入PaddleOCR")

def check_language_support():
    """检查语言支持"""
    print("\n=== 检查语言支持 ===")
    
    try:
        from paddleocr import PaddleOCR
        
        # 测试中文支持
        print("测试中文支持...")
        ocr_ch = PaddleOCR(
            use_angle_cls=True,
            lang='ch',
            use_gpu=False,
            show_log=False
        )
        print("[OK] 中文支持正常")
        
        # 测试英文支持
        print("测试英文支持...")
        ocr_en = PaddleOCR(
            use_angle_cls=True,
            lang='en',
            use_gpu=False,
            show_log=False
        )
        print("[OK] 英文支持正常")
        
    except Exception as e:
        print(f"[ERROR] 语言支持测试失败: {e}")

def main():
    """主函数"""
    print("OCR简单测试工具")
    print("=" * 30)
    
    # 基本测试
    if test_basic_ocr():
        print("\n[OK] 基本OCR功能正常")
    else:
        print("\n[ERROR] 基本OCR功能异常")
        return
    
    # 模型配置测试
    check_model_config()
    
    # 语言支持测试
    check_language_support()
    
    print("\n=== 测试完成 ===")
    print("如果所有测试都通过，说明OCR配置正常")
    print("如果识别效果不好，可能是以下原因：")
    print("1. 图片质量问题（模糊、分辨率低）")
    print("2. 文字太小或字体特殊")
    print("3. 图片对比度不够")
    print("4. 文字角度倾斜")

if __name__ == "__main__":
    main()
